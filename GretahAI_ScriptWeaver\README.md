# GretahAI ScriptWeaver

[![Cogniron Logo](https://cogniron.com/wp-content/uploads/2024/10/image-69.png)](https://cogniron.com/)

**GretahAI ScriptWeaver** is a tool for generating executable PyTest automation scripts from test cases stored in Excel files. It uses AI to convert test cases to automation-ready step tables, detect UI elements, and generate robust, maintainable test scripts.

## Features

- Parse test cases from Excel files
- Convert test cases to automation-ready step tables using Google AI
- Automatically detect UI elements from any website
- Interactive element selection for precise locator identification
- Map UI elements to XPath or CSS selectors
- Generate context-aware test data for form inputs
- Generate executable PyTest scripts using AI
- Run generated test scripts directly from the application
- Optimize and consolidate scripts using AI for best practices
- Capture screenshots on test failures
- Step-by-step workflow with manual progression
- Comprehensive logging and debugging capabilities

## Project Structure

The project is organized into a fully modular structure with clean separation of concerns. All 8 application stages are now in dedicated files for improved maintainability:

```
GretahAI_ScriptWeaver/
  ├── __init__.py             # Main package initialization
  ├── app.py                  # Streamlit UI application
  ├── stages/                 # Fully modular stage functions (completed refactoring)
  │   ├── __init__.py         # Centralized stage imports and API
  │   ├── stage1.py           # Stage 1: Upload Excel File
  │   ├── stage2.py           # Stage 2: Website Configuration
  │   ├── stage3.py           # Stage 3: Test Case Analysis and Conversion
  │   ├── stage4.py           # Stage 4: UI Element Detection and Step Selection
  │   ├── stage5.py           # Stage 5: Test Data Configuration
  │   ├── stage6.py           # Stage 6: Test Script Generation
  │   ├── stage7.py           # Stage 7: Test Script Execution
  │   └── stage8.py           # Stage 8: Script Consolidation and Optimization
  ├── state_manager.py        # Centralized state management
  ├── helpers_pure.py         # Pure helper functions (no state dependencies)
  ├── test_data_manager.py    # Test data generation and management
  ├── core/                   # Core functionality as a module
  │   ├── __init__.py         # Core module initialization
  │   ├── element_detection.py # UI element detection
  │   ├── element_matching.py  # Element matching with AI
  │   ├── interactive_selector.py # Interactive element selection
  │   ├── helpers_diff.py     # Differential analysis helpers
  │   └── ai.py               # AI/LLM integration and optimization
  ├── ui/                     # UI components
  │   ├── __init__.py         # UI module initialization
  │   └── components.py       # Reusable UI components
  ├── generated_tests/        # Generated test scripts
  ├── screenshots/            # Screenshots captured during test execution
  ├── temp_uploads/           # Temporary storage for uploaded files
  ├── detected_elements/      # JSON files with detected UI elements
  └── ai_logs/                # AI interaction logs for debugging
```

## Architecture

GretahAI ScriptWeaver follows a fully modular architecture with complete separation of concerns:

### 1. **Modular Stage Architecture**
   - **8 Dedicated Stage Files**: Each application stage (1-8) is now in its own dedicated file
   - **Centralized Import System**: All stages are imported through `stages/__init__.py` for API consistency
   - **Individual Responsibility**: Each stage file handles only its specific functionality
   - **Easy Maintenance**: Developers can work on individual stages without affecting others

### 2. **Centralized State Management**
   - **StateManager Pattern**: Uses a StateManager dataclass to hold all session state
   - **Consistent State Mutations**: Provides helper methods for state changes with logging
   - **Cross-Stage Persistence**: Maintains consistent state across all application stages
   - **Debug Support**: Includes comprehensive logging and debugging capabilities

### 3. **Unified Import System**
   - **Single Entry Point**: All stage functions imported from the unified `stages` package
   - **API Compatibility**: Maintains backward compatibility with existing code
   - **Clean Dependencies**: Clear import hierarchy prevents circular dependencies

### 4. **Clean Separation of Concerns**
   - **Pure Helper Functions**: Extracted to separate modules (helpers_pure.py)
   - **AI Integration**: Centralized in core/ai.py with comprehensive logging
   - **UI Components**: Reusable components in dedicated ui/ module
   - **Core Functionality**: Element detection, matching, and selection in core/ module

### 5. **Enhanced Logging and Debugging**
   - **Stage-Specific Logging**: Each stage has its own logger namespace
   - **AI Interaction Logs**: Complete AI API call traces stored in ai_logs/
   - **State Change Tracking**: Detailed logging of all state mutations
   - **Debug Information**: Comprehensive debug panels for troubleshooting

## Application Workflow

GretahAI ScriptWeaver follows a structured, step-by-step workflow:

1. **Stage 1: Upload Excel File**
   - Upload an Excel file containing test cases
   - Preview test cases in a data table

2. **Stage 2: Enter Website URL**
   - Provide the URL of the website to test
   - Configure Google AI API key for AI features
   - Set options for element detection and matching

3. **Stage 3: Test Case Analysis and Conversion**
   - Select a test case from the uploaded Excel file
   - Convert the test case to an automation-ready step table using Google AI
   - Review the converted step table in both markdown and JSON formats

4. **Stage 4: UI Element Detection and Step Selection**
   - Process test steps sequentially with manual progression
   - Detect UI elements automatically or select them interactively
   - Match UI elements to test steps using AI

5. **Stage 5: Configure Test Data**
   - Generate realistic test data for form inputs
   - Customize test data values if needed
   - Skip test data configuration for navigation steps

6. **Stage 6: Generate Test Script**
   - Generate a PyTest script for the selected step
   - Review the generated script
   - Save the script to a file

7. **Stage 7: Run Test Script**
   - Run the generated script using PyTest
   - View test results and any captured screenshots
   - Automatically advance to the next step or proceed to optimization

8. **Stage 8: Script Consolidation and Optimization**
   - Combine all test step scripts into a single cohesive script
   - Optimize the combined script using Google AI for best practices
   - Download the final optimized PyTest script
   - Return to Stage 3 to select a new test case

## Setup

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Create a `config.json` file with your Google API key:

```json
{
    "google_api_key": "YOUR_API_KEY_HERE"
}
```

## Documentation

For detailed technical information, please refer to our comprehensive documentation:

| Document | Description |
|----------|-------------|
| [**Development Guidelines**](docs/DEVELOPMENT.md) | Comprehensive development standards, patterns, and best practices |
| [**API Documentation**](docs/API.md) | Complete API reference with function signatures and examples |
| [**Contributing Guide**](docs/CONTRIBUTING.md) | How to contribute to the project, including setup and workflow |
| [**Documentation Index**](docs/README.md) | Central navigation for all documentation |
| [**Changelog**](CHANGELOG.md) | Version history and release notes |

## Usage

### Running the Application

Start the Streamlit application:

```bash
cd GretahAI_ScriptWeaver
streamlit run app.py
```

The application will open in your default web browser at http://localhost:8501.

### Test Case Format

The Excel file should contain test cases with the following columns:

- **Test Case ID**: Unique identifier for the test case
- **Test Case Objective**: Description of what the test case is testing
- **Steps**: A list of steps with:
  - **Step No**: Step number
  - **Test Steps**: Description of the action to perform
  - **Expected Result**: Expected outcome of the action

### Generated Test Scripts

Generated test scripts follow these principles:

- Use Selenium WebDriver with PyTest for browser automation
- Implement explicit waits for reliable element detection
- Include appropriate assertions based on expected results
- Capture screenshots on test failures
- Use a modular fixture-based approach for WebDriver setup
- Add random delays between actions to simulate human behavior

## Key Features

### 1. Interactive Element Selection

The application provides an interactive element selection feature:

- Opens a controlled browser window where users can manually select UI elements
- Highlights elements on hover to make selection easier
- Captures precise locator information (CSS selector, XPath, ID, etc.) for the selected element
- Provides immediate feedback with detailed element information
- Automatically creates element matches for the manually selected element

### 2. AI-Powered Step Table Conversion

The application uses Google AI to convert test cases to automation-ready step tables:

- Converts natural language test steps to structured step tables
- Identifies appropriate locator strategies for each step
- Determines test data parameters and assertion types
- Analyzes steps to determine if UI element detection is needed
- Provides both markdown and JSON formats for the step table

### 3. Context-Aware Element Mapping

The application provides context-aware element mapping for test steps:

- Filters UI elements focusing on those most relevant for QA automation
- Creates AI prompts that include context from previous test steps
- Uses reasoning to explain why certain elements are matched with steps
- Allows manual selection of elements for complex web pages

### 4. Intelligent Test Data Generation

The application generates realistic test data for test steps:

- Analyzes step context to determine required test data
- Generates appropriate test data based on field type and purpose
- Provides realistic values for common data types (emails, names, addresses, etc.)
- Skips test data generation for navigation steps
- Allows customization of generated test data

### 5. Step-by-Step Workflow

The application follows a structured workflow with manual progression:

- Processes test steps sequentially
- Provides clear visual indicators for the current step
- Allows users to proceed to the next step after completing the current one
- Maintains state across steps for a consistent experience
- Provides debug information for troubleshooting

## Troubleshooting

* **API Key Issues:** Ensure your Google API key is correct in the `config.json` file.
* **Element Detection Issues:** Try using a different browser or updating the browser driver.
* **Interactive Selection Issues:** If the browser window doesn't open or elements can't be selected, try updating your Chrome browser and WebDriver.
* **Script Generation Issues:** Check the Excel file format and ensure the test steps are clear.
* **State Management Issues:** If the application gets stuck, use the debug panel to reset state variables.

## Integration with Other GRETAH Applications

GretahAI ScriptWeaver integrates with:

* **GretahAI CaseForge:** Import test cases from CaseForge.
* **GretahAI TestInsight:** Export generated test scripts for execution and analysis.

## Contributing

We welcome contributions to GretahAI ScriptWeaver! The modular architecture makes it easy to contribute to specific areas.

### Quick Start for Contributors

1. **Read the Contributing Guide**: Start with our comprehensive [Contributing Guide](docs/CONTRIBUTING.md)
2. **Understand the Architecture**: Review the [Development Guidelines](docs/DEVELOPMENT.md)
3. **Check the API**: Reference the [API Documentation](docs/API.md) for implementation details

### Areas for Contribution

- **Stage Development**: Enhance existing stages or add new functionality
- **Core Features**: Improve AI integration, element detection, and state management
- **UI/UX**: Enhance user interface and experience
- **Documentation**: Help improve and maintain our documentation
- **Testing**: Expand test coverage and reliability

### Development Process

1. Fork the repository and create a feature branch
2. Follow our established patterns and guidelines
3. Include tests and documentation for new features
4. Submit a pull request with a detailed description

For detailed information, see our [Contributing Guide](docs/CONTRIBUTING.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For questions or support, please contact:
- Email: <EMAIL>
- Website: https://cogniron.com
