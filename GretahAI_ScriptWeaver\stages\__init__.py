"""
Stages package for GretahAI ScriptWeaver

This package contains all the individual stage modules for the application workflow.
Each stage is now in its own dedicated file for better maintainability.

The stages are organized as follows:
- stage1.py: Upload Excel File
- stage2.py: Website Configuration
- stage3.py: Test Case Analysis and Conversion
- stage4.py: UI Element Detection and Test Case Step Selection
- stage5.py: Test Data Configuration
- stage6.py: Test Script Generation
- stage7.py: Test Script Execution
- stage8.py: Script Consolidation and Optimization

This maintains the existing API so app.py can import stages without modification.
"""

# Import all stage functions to maintain the existing API
from stages.stage1 import stage1_upload_excel
from stages.stage2 import stage2_enter_website
from stages.stage3 import stage3_convert_test_case
from stages.stage4 import stage4_ui_detection_and_matching
from stages.stage5 import stage5_test_data
from stages.stage6 import stage6_generate_script
from stages.stage7 import stage7_run_script, advance_to_next_step
from stages.stage8 import stage8_optimize_script

# Export all stage functions for easy importing
__all__ = [
    'stage1_upload_excel',
    'stage2_enter_website',
    'stage3_convert_test_case',
    'stage4_ui_detection_and_matching',
    'stage5_test_data',
    'stage6_generate_script',
    'stage7_run_script',
    'stage8_optimize_script',
    'advance_to_next_step'
]
