/*
 * ==========================================================================
 * STYLE SHEET: style.css
 * ==========================================================================
 *
 * Description:
 * This CSS file defines the visual styling for the Autotest application.
 * It includes definitions for color themes (light and dark modes),
 * typography, layout elements, animations, and specific component styles
 * like panels, cards, buttons, status indicators, and the sidebar.
 *
 * Structure:
 * 1.  `:root`        - Defines global CSS variables for colors.
 * 2.  Light Mode     - Styles applied when the 'light-mode' class is active.
 * 3.  Dark Mode      - Styles applied when the 'dark-mode' class is active.
 * 4.  Headers        - Styles for main and sub-headers, including animations.
 * 5.  Panels         - Styles for different types of informational panels
 *                      (feature, analytics, alert).
 * 6.  Cards          - Styles for professional card layout (`.pro-card`).
 * 7.  Buttons        - Base styles and hover effects for Streamlit buttons (`.stButton`).
 * 8.  Sidebar        - Styles and animations for the Streamlit sidebar (`.stSidebar`).
 * 9.  Status         - Styles for visual status indicators (passed, failed, blocked).
 * 10. Action Buttons - Basic styling for action buttons.
 * 11. Logo           - Styles for the logo container and text.
 * 12. Footer         - Styles for the application footer (`.pro-footer`).
 * 13. Animations     - Keyframe definitions for animations (`fadeIn`, `slideIn`).
 *
 * Usage:
 * This stylesheet is intended to be loaded by the Streamlit application to
 * provide a consistent and professional look and feel, supporting both
 * light and dark themes and enhancing user experience with subtle animations
 * and clear visual cues.
 *
 * ==========================================================================
 */

:root {
    /* TestInsight-specific color palette */
    --primary-color: #3F51B5; /* Indigo */
    --secondary-color: #009688; /* Teal */
    --background-color-light: #F5F7FA; /* Light Gray with blue tint */
    --background-color-dark: #1A1F35; /* Dark Blue-Gray */
    --text-color-light: #37474F; /* Dark Blue-Gray */
    --text-color-dark: #ECEFF1; /* Light Blue-Gray */
    --highlight-color: #FF5722; /* Deep Orange */
    --success-color: #4CAF50; /* Green */
    --warning-color: #FFC107; /* Amber */
    --error-color: #F44336; /* Red */
    --info-color: #2196F3; /* Blue */
}
/* Light Mode Styles */
.light-mode {
    background-color: var(--background-color-light);
    color: var(--text-color-light);
}
.light-mode .stButton > button {
    background-color: var(--primary-color);
    color: var(--text-color-light); /* Adjusted for better contrast on blue */
}
/* Dark Mode Styles */
.dark-mode {
    background-color: var(--background-color-dark);
    color: var(--text-color-dark);
}
.dark-mode .stButton > button {
    background-color: var(--secondary-color);
    color: var(--text-color-dark);
}
/* Header Animations - TestInsight specific */
.main-header {
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
}
.sub-header {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 600;
    border-left: 4px solid var(--primary-color);
    padding-left: 10px;
}
/* Professional panels - TestInsight specific */
.feature-panel {
    background-color: rgba(63, 81, 181, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: 12px;
    margin: 12px 0;
    font-size: 0.95rem;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.analytics-panel {
    background-color: rgba(0, 150, 136, 0.05);
    border-left: 3px solid var(--secondary-color);
    padding: 12px;
    margin: 12px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.alert-panel {
    background-color: rgba(244, 67, 54, 0.05);
    border-left: 3px solid var(--error-color);
    padding: 12px;
    margin: 12px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.insight-panel {
    background-color: rgba(33, 150, 243, 0.05);
    border-left: 3px solid var(--info-color);
    padding: 12px;
    margin: 12px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
/* Professional card layout */
.pro-card {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
/* Button Hover Effects */
.stButton > button:hover {
    background-color: var(--highlight-color);
    transform: scale(1.05);
    transition: all 0.3s ease-in-out;
}
/* Sidebar Animation */
.stSidebar {
    animation: slideIn 0.5s ease-in-out;
}
@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}
/* Status indicators */
.status-passed {
    color: #26A69A;
    font-weight: bold;
}
.status-failed {
    color: #EF5350;
    font-weight: bold;
}
.status-blocked { /* Renamed from unknown */
    color: #FFA726;
    font-weight: bold;
}
/* Action buttons */
.action-button {
    margin-top: 10px;
}
/* Logo container */
.logo-container {
    text-align: center;
    margin-bottom: 10px;
}
.logo-text {
    font-size: 1.4rem;
    font-weight: bold;
    margin-top: 5px;
}
/* Footer style - TestInsight specific */
.pro-footer {
    text-align: center;
    padding: 15px;
    margin-top: 30px;
    font-size: 0.9rem;
    border-top: 2px solid rgba(63, 81, 181, 0.2);
    background-color: rgba(63, 81, 181, 0.05);
    color: var(--primary-color);
    font-weight: 500;
}

.testinsight-footer {
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    font-size: 0.9rem;
    background: linear-gradient(to right, rgba(63, 81, 181, 0.1), rgba(0, 150, 136, 0.1));
    border-top: 1px solid var(--primary-color);
    border-bottom: 1px solid var(--secondary-color);
    color: var(--text-color-light);
}
/* Fade-in Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
