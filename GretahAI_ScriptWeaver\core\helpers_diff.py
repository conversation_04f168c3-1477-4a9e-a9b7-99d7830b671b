"""
Helper functions for generating and displaying diffs between scripts.

This module provides functions for:
1. Generating HTML diffs between step-specific and merged scripts
2. Highlighting the differences between scripts
3. Analyzing which parts of the merged script came from previous steps
"""

import difflib
import re
import logging
from typing import Tuple, List, Dict, Optional

# Set up logging
logger = logging.getLogger(__name__)

def generate_html_diff(step_specific_script: str, merged_script: str) -> str:
    """
    Generate an HTML diff between the step-specific script and the merged script.
    
    Args:
        step_specific_script (str): The script for the current step only
        merged_script (str): The merged script combining all steps
        
    Returns:
        str: HTML representation of the diff
    """
    try:
        # Split the scripts into lines
        step_specific_lines = step_specific_script.splitlines()
        merged_lines = merged_script.splitlines()
        
        # Generate the diff
        diff = difflib.HtmlDiff(tabsize=4, wrapcolumn=80)
        html_diff = diff.make_file(
            step_specific_lines, 
            merged_lines,
            fromdesc="Step-Specific Script",
            todesc="Merged Script"
        )
        
        # Customize the HTML diff for better integration with Streamlit
        # Remove the default styles that might conflict with Streamlit
        html_diff = html_diff.replace(
            '<style type="text/css">',
            '<style type="text/css">\n/* Custom styles for Streamlit integration */\n'
        )
        
        # Add custom styles for better visibility in Streamlit
        html_diff = html_diff.replace(
            '</style>',
            'table.diff {width: 100%; border-collapse: collapse; font-family: monospace; font-size: 14px;}\n'
            'table.diff td {padding: 3px 5px; vertical-align: top; white-space: pre-wrap;}\n'
            'table.diff .diff_add {background-color: #aaffaa; color: #000000;}\n'
            'table.diff .diff_chg {background-color: #ffff77; color: #000000;}\n'
            'table.diff .diff_sub {background-color: #ffaaaa; color: #000000;}\n'
            'table.diff th {background-color: #e0e0e0; padding: 5px; text-align: center;}\n'
            '</style>'
        )
        
        return html_diff
    except Exception as e:
        logger.error(f"Error generating HTML diff: {e}")
        return f"<p>Error generating diff: {str(e)}</p>"

def analyze_script_origins(step_specific_script: str, merged_script: str) -> Dict[int, str]:
    """
    Analyze which parts of the merged script came from previous steps vs. the current step.
    
    Args:
        step_specific_script (str): The script for the current step only
        merged_script (str): The merged script combining all steps
        
    Returns:
        Dict[int, str]: Dictionary mapping line numbers to origin ('current' or 'previous')
    """
    try:
        # Split the scripts into lines
        step_specific_lines = step_specific_script.splitlines()
        merged_lines = merged_script.splitlines()
        
        # Initialize the origins dictionary
        origins = {}
        
        # Use difflib to compare the scripts
        matcher = difflib.SequenceMatcher(None, step_specific_lines, merged_lines)
        
        # Process the opcodes to determine the origin of each line
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # Lines that are in both scripts came from the current step
                for i in range(j1, j2):
                    origins[i] = 'current'
            elif tag == 'insert' or tag == 'replace':
                # Lines that are only in the merged script came from previous steps
                for i in range(j1, j2):
                    origins[i] = 'previous'
        
        return origins
    except Exception as e:
        logger.error(f"Error analyzing script origins: {e}")
        return {}

def generate_annotated_script(merged_script: str, origins: Dict[int, str]) -> str:
    """
    Generate an annotated version of the merged script with origin indicators.
    
    Args:
        merged_script (str): The merged script combining all steps
        origins (Dict[int, str]): Dictionary mapping line numbers to origin
        
    Returns:
        str: Annotated script with origin indicators
    """
    try:
        # Split the script into lines
        lines = merged_script.splitlines()
        
        # Add annotations to each line
        annotated_lines = []
        for i, line in enumerate(lines):
            origin = origins.get(i, 'unknown')
            if origin == 'current':
                # Lines from the current step
                annotated_lines.append(f"{line}  # [Current Step]")
            elif origin == 'previous':
                # Lines from previous steps
                annotated_lines.append(f"{line}  # [Previous Step]")
            else:
                # Unknown origin
                annotated_lines.append(f"{line}  # [Unknown Origin]")
        
        return '\n'.join(annotated_lines)
    except Exception as e:
        logger.error(f"Error generating annotated script: {e}")
        return merged_script

def generate_colored_html_script(merged_script: str, origins: Dict[int, str]) -> str:
    """
    Generate an HTML version of the merged script with color-coded origins.
    
    Args:
        merged_script (str): The merged script combining all steps
        origins (Dict[int, str]): Dictionary mapping line numbers to origin
        
    Returns:
        str: HTML script with color-coded origins
    """
    try:
        # Split the script into lines
        lines = merged_script.splitlines()
        
        # Generate HTML with color-coded lines
        html_lines = ['<pre style="font-family: monospace; font-size: 14px; line-height: 1.5;">']
        
        for i, line in enumerate(lines):
            origin = origins.get(i, 'unknown')
            
            # Escape HTML special characters
            escaped_line = line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
            
            if origin == 'current':
                # Lines from the current step - blue
                html_lines.append(f'<span style="background-color: #e6f3ff; display: block;">{escaped_line}</span>')
            elif origin == 'previous':
                # Lines from previous steps - light green
                html_lines.append(f'<span style="background-color: #e6ffe6; display: block;">{escaped_line}</span>')
            else:
                # Unknown origin - gray
                html_lines.append(f'<span style="background-color: #f0f0f0; display: block;">{escaped_line}</span>')
        
        html_lines.append('</pre>')
        return '\n'.join(html_lines)
    except Exception as e:
        logger.error(f"Error generating colored HTML script: {e}")
        return f"<pre>{merged_script}</pre>"
