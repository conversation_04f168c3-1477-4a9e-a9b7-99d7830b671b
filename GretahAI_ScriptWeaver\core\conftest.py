"""
Pytest configuration file for the test script generator.
This module provides fixtures and hooks for pytest.
"""

import pytest
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

@pytest.fixture
def driver():
    """
    Fixture to create and return a WebDriver instance.
    
    Returns:
        WebDriver: A configured WebDriver instance
    """
    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    
    # Create the WebDriver instance
    driver = webdriver.Chrome(
        service=Service(ChromeDriverManager().install()),
        options=chrome_options
    )
    
    # Set implicit wait time
    driver.implicitly_wait(10)
    
    # Return the driver to the test
    yield driver
    
    # Quit the driver after the test
    driver.quit()

@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    Hook to take screenshots on test failures.
    
    Args:
        item: Test item
        call: Test call
    """
    # Execute the test
    outcome = yield
    report = outcome.get_result()
    
    # Take a screenshot if the test failed
    if report.when == "call" and report.failed:
        driver = item.funcargs.get("driver")
        if driver:
            # Create screenshots directory if it doesn't exist
            os.makedirs("screenshots", exist_ok=True)
            
            # Take a screenshot
            screenshot_path = f"screenshots/{item.name}.png"
            driver.save_screenshot(screenshot_path)
            print(f"Screenshot saved to {screenshot_path}")
