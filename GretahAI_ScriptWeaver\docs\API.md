# API Documentation

This document provides comprehensive API documentation for GretahAI ScriptWeaver's modular architecture.

## Table of Contents

- [Stage Function Imports](#stage-function-imports)
- [Stage Function Signatures](#stage-function-signatures)
- [Core Module Functions](#core-module-functions)
- [State Manager API](#state-manager-api)
- [Helper Functions](#helper-functions)
- [Configuration API](#configuration-api)

## Stage Function Imports

All stage functions are imported from the unified `stages` package:

```python
from stages import (
    stage1_upload_excel,           # Stage 1: Upload Excel File
    stage2_enter_website,          # Stage 2: Website Configuration
    stage3_convert_test_case,      # Stage 3: Test Case Analysis and Conversion
    stage4_ui_detection_and_matching,  # Stage 4: UI Element Detection and Step Selection
    stage5_test_data,              # Stage 5: Test Data Configuration
    stage6_generate_script,        # Stage 6: Test Script Generation
    stage7_run_script,             # Stage 7: Test Script Execution
    stage8_optimize_script,        # Stage 8: Script Consolidation and Optimization
    advance_to_next_step           # Helper function for step progression
)
```

## Stage Function Signatures

All stage functions follow a consistent signature pattern:

```python
def stage_function(state: StateManager) -> None:
    """
    Stage function description.

    Args:
        state (StateManager): The application state manager instance
    
    Returns:
        None: Functions modify state in-place and handle UI rendering
    
    Raises:
        Exception: Various exceptions based on stage-specific operations
    """
```

### Individual Stage Functions

#### Stage 1: Upload Excel File
```python
def stage1_upload_excel(state: StateManager) -> None:
    """
    Handle Excel file upload and preview test cases.
    
    Features:
    - File upload with validation
    - Excel parsing and preview
    - Test case data table display
    """
```

#### Stage 2: Website Configuration
```python
def stage2_enter_website(state: StateManager) -> None:
    """
    Configure website URL and AI settings.
    
    Features:
    - Website URL input and validation
    - Google AI API key configuration
    - Element detection options
    """
```

#### Stage 3: Test Case Analysis and Conversion
```python
def stage3_convert_test_case(state: StateManager) -> None:
    """
    Convert test cases to automation-ready step tables.
    
    Features:
    - Test case selection
    - AI-powered step table conversion
    - Markdown and JSON preview
    """
```

#### Stage 4: UI Element Detection and Step Selection
```python
def stage4_ui_detection_and_matching(state: StateManager) -> None:
    """
    Detect UI elements and match them to test steps.
    
    Features:
    - Automatic element detection
    - Interactive element selection
    - AI-powered element matching
    """
```

#### Stage 5: Test Data Configuration
```python
def stage5_test_data(state: StateManager) -> None:
    """
    Generate and configure test data for form inputs.
    
    Features:
    - Realistic test data generation
    - Manual data entry options
    - Context-aware data suggestions
    """
```

#### Stage 6: Test Script Generation
```python
def stage6_generate_script(state: StateManager) -> None:
    """
    Generate PyTest scripts for individual test steps.
    
    Features:
    - AI-powered script generation
    - Script preview and editing
    - File saving with timestamps
    """
```

#### Stage 7: Test Script Execution
```python
def stage7_run_script(state: StateManager) -> None:
    """
    Execute generated test scripts and display results.
    
    Features:
    - PyTest execution
    - Result display and analysis
    - Screenshot capture on failures
    """
```

#### Stage 8: Script Consolidation and Optimization
```python
def stage8_optimize_script(state: StateManager) -> None:
    """
    Consolidate and optimize test scripts using AI.
    
    Features:
    - Script merging and optimization
    - Performance improvements
    - Download functionality
    """
```

## Core Module Functions

### AI Integration (core/ai.py)

```python
from core.ai import (
    generate_llm_response,         # Main AI API interface
    optimize_script_with_ai,       # Script optimization
    merge_scripts_with_ai          # Script merging
)

def generate_llm_response(
    prompt: str,
    context: str = "",
    model: str = "gemini-2.0-flash",
    max_tokens: int = 4000
) -> str:
    """
    Generate AI response with comprehensive logging.
    
    Args:
        prompt: The main prompt for the AI
        context: Additional context information
        model: AI model to use
        max_tokens: Maximum response tokens
    
    Returns:
        str: AI-generated response
    
    Raises:
        Exception: API errors, authentication issues
    """
```

### Element Detection (core/element_detection.py)

```python
from core.element_detection import (
    detect_elements,               # Automatic element detection
    save_detected_elements         # Save elements to JSON
)

def detect_elements(url: str, options: dict = None) -> list:
    """
    Detect UI elements from a webpage.
    
    Args:
        url: Target webpage URL
        options: Detection configuration options
    
    Returns:
        list: Detected UI elements with metadata
    """
```

### Element Matching (core/element_matching.py)

```python
from core.element_matching import (
    match_elements_with_ai         # AI-powered element matching
)

def match_elements_with_ai(
    elements: list,
    test_step: dict,
    context: str = ""
) -> dict:
    """
    Match UI elements to test steps using AI.
    
    Args:
        elements: List of detected UI elements
        test_step: Test step information
        context: Additional context for matching
    
    Returns:
        dict: Element matching results with confidence scores
    """
```

## State Manager API

The StateManager provides centralized state management:

```python
from state_manager import StateManager

# Get state instance
state = StateManager.get(st)

# State update methods
state.update_step_progress(current_step_index=1, step_ready_for_script=True)
state.reset_step_state(confirm=True, reason="User requested reset")
state.reset_test_case_state(confirm=True, reason="Starting new test case")
```

### StateManager Methods

```python
class StateManager:
    def update_step_progress(
        self,
        current_step_index: int,
        step_ready_for_script: bool = False
    ) -> None:
        """Update step progression state."""
    
    def reset_step_state(
        self,
        confirm: bool = False,
        reason: str = ""
    ) -> None:
        """Reset current step state."""
    
    def reset_test_case_state(
        self,
        confirm: bool = False,
        reason: str = ""
    ) -> None:
        """Reset entire test case state."""
    
    def get_current_step(self) -> dict:
        """Get current step information."""
    
    def set_current_step(self, step_data: dict) -> None:
        """Set current step data."""
```

## Helper Functions

### Pure Helper Functions (helpers_pure.py)

```python
from helpers_pure import (
    safe_get_list_item,           # Safe list access
    validate_file_extension,      # File validation
    format_timestamp,             # Time formatting
    sanitize_filename             # Filename sanitization
)
```

### Test Data Management (test_data_manager.py)

```python
from test_data_manager import (
    generate_test_data,           # Generate realistic test data
    validate_test_data,           # Validate data formats
    save_test_data               # Save data to files
)
```

## Configuration API

### Configuration Management (core/config.py)

```python
from core.config import (
    load_config,                  # Load configuration
    save_config,                  # Save configuration
    get_api_key                   # Get API keys securely
)

def load_config(config_path: str = "config.json") -> dict:
    """
    Load application configuration.
    
    Args:
        config_path: Path to configuration file
    
    Returns:
        dict: Configuration settings
    """
```

## Error Handling

All API functions follow consistent error handling patterns:

```python
try:
    result = api_function(parameters)
except SpecificException as e:
    logger.error(f"Specific error in function: {e}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {e}", exc_info=True)
    raise
```

## Logging Integration

All API functions include comprehensive logging:

```python
import logging
logger = logging.getLogger("ScriptWeaver.module_name")

def api_function():
    logger.info("Function started")
    # Function implementation
    logger.info("Function completed successfully")
```

## Type Hints and Validation

All API functions include proper type hints:

```python
from typing import List, Dict, Optional, Union

def typed_function(
    required_param: str,
    optional_param: Optional[int] = None,
    list_param: List[Dict[str, str]] = None
) -> Union[str, None]:
    """Function with comprehensive type hints."""
```

For more detailed implementation examples, see [DEVELOPMENT.md](DEVELOPMENT.md).
