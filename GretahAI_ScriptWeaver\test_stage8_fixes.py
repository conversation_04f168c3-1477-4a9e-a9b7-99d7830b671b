#!/usr/bin/env python3
"""
Test script to verify Stage 8 fixes for GretahAI_ScriptWeaver

This script tests the key fixes implemented for Stage 7 and Stage 8 workflow issues:
1. StateManager optimization_start_time attribute
2. State reset functionality
3. Error handling improvements
4. Workflow transition logic

Run this script to validate that the fixes are working correctly.
"""

import sys
import os
from datetime import datetime
from dataclasses import asdict

# Add the current directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_state_manager_attributes():
    """Test that StateManager has all required optimization attributes."""
    print("Testing StateManager attributes...")
    
    try:
        from state_manager import StateManager
        
        # Create a new StateManager instance
        state = StateManager()
        
        # Test that all optimization attributes exist
        required_attrs = [
            'optimization_in_progress',
            'optimization_complete', 
            'optimization_start_time',
            'optimized_script_path',
            'optimized_script_content',
            'optimization_chunks',
            'combined_script_path',
            'combined_script_content'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(state, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ FAIL: Missing attributes: {missing_attrs}")
            return False
        else:
            print("✅ PASS: All required optimization attributes present")
            
        # Test that optimization_start_time can be set to datetime
        state.optimization_start_time = datetime.now()
        if isinstance(state.optimization_start_time, datetime):
            print("✅ PASS: optimization_start_time accepts datetime values")
        else:
            print("❌ FAIL: optimization_start_time does not accept datetime values")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Error testing StateManager: {e}")
        return False

def test_state_reset_functionality():
    """Test that state reset properly clears optimization state."""
    print("\nTesting state reset functionality...")
    
    try:
        from state_manager import StateManager
        
        # Create a StateManager instance and set some optimization state
        state = StateManager()
        state.optimization_in_progress = True
        state.optimization_complete = True
        state.optimization_start_time = datetime.now()
        state.optimized_script_path = "/test/path"
        state.optimized_script_content = "test content"
        state.combined_script_path = "/test/combined"
        state.combined_script_content = "combined content"
        
        # Reset test case state
        result = state.reset_test_case_state(confirm=True, reason="Testing reset")
        
        if not result:
            print("❌ FAIL: reset_test_case_state returned False")
            return False
            
        # Check that optimization state was reset
        if (state.optimization_in_progress or 
            state.optimization_complete or
            state.optimization_start_time is not None or
            state.optimized_script_path is not None or
            state.optimized_script_content != "" or
            state.combined_script_path is not None or
            state.combined_script_content is not None):
            print("❌ FAIL: Optimization state not properly reset")
            print(f"  optimization_in_progress: {state.optimization_in_progress}")
            print(f"  optimization_complete: {state.optimization_complete}")
            print(f"  optimization_start_time: {state.optimization_start_time}")
            print(f"  optimized_script_path: {state.optimized_script_path}")
            print(f"  combined_script_path: {state.combined_script_path}")
            return False
        else:
            print("✅ PASS: Optimization state properly reset")
            return True
            
    except Exception as e:
        print(f"❌ FAIL: Error testing state reset: {e}")
        return False

def test_stage8_import():
    """Test that Stage 8 module can be imported and has required functions."""
    print("\nTesting Stage 8 module import...")
    
    try:
        from stages.stage8 import stage8_optimize_script
        print("✅ PASS: Stage 8 module imported successfully")
        
        # Check that the function exists and is callable
        if callable(stage8_optimize_script):
            print("✅ PASS: stage8_optimize_script function is callable")
            return True
        else:
            print("❌ FAIL: stage8_optimize_script is not callable")
            return False
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import Stage 8 module: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Error testing Stage 8 import: {e}")
        return False

def test_ai_function_import():
    """Test that optimize_script_with_ai function can be imported."""
    print("\nTesting AI function import...")
    
    try:
        from core.ai import optimize_script_with_ai
        print("✅ PASS: optimize_script_with_ai imported successfully")
        
        if callable(optimize_script_with_ai):
            print("✅ PASS: optimize_script_with_ai function is callable")
            return True
        else:
            print("❌ FAIL: optimize_script_with_ai is not callable")
            return False
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import optimize_script_with_ai: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Error testing AI function import: {e}")
        return False

def main():
    """Run all tests and report results."""
    print("=" * 60)
    print("GretahAI_ScriptWeaver Stage 8 Fixes Validation")
    print("=" * 60)
    
    tests = [
        test_state_manager_attributes,
        test_state_reset_functionality,
        test_stage8_import,
        test_ai_function_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Stage 8 fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
