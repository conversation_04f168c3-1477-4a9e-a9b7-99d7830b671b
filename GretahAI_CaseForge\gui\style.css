/*
 * ==========================================================================
 * STYLE SHEET: style.css for GretahAI CaseForge
 * ==========================================================================
 *
 * Description:
 * This CSS file defines the visual styling for the GretahAI CaseForge application.
 * It includes definitions for color themes, typography, layout elements, 
 * animations, and specific component styles.
 *
 * Structure:
 * 1.  `:root`        - Defines global CSS variables for colors.
 * 2.  Light Mode     - Styles applied when the 'light-mode' class is active.
 * 3.  Dark Mode      - Styles applied when the 'dark-mode' class is active.
 * 4.  Headers        - Styles for main and sub-headers, including animations.
 * 5.  Panels         - Styles for different types of informational panels.
 * 6.  Cards          - Styles for professional card layout.
 * 7.  Buttons        - Base styles and hover effects for Streamlit buttons.
 * 8.  Sidebar        - Styles and animations for the Streamlit sidebar.
 * 9.  Status         - Styles for visual status indicators.
 * 10. Action Buttons - Basic styling for action buttons.
 * 11. Logo           - Styles for the logo container and text.
 * 12. Footer         - Styles for the application footer.
 * 13. Animations     - Keyframe definitions for animations.
 *
 * ==========================================================================
 */

:root {
    /* CaseForge-specific color palette */
    --primary-color: #4A6FE3; /* Royal Blue */
    --secondary-color: #6C8EFF; /* Light Blue */
    --accent-color: #FF9D4D; /* Orange */
    --background-color-light: #F8F9FD; /* Very Light Blue-Gray */
    --background-color-dark: #1A1E2E; /* Dark Blue-Gray */
    --text-color-light: #2C3E50; /* Dark Blue-Gray */
    --text-color-dark: #090e0f; /* Light Gray */
    --success-color: #2ECC71; /* Green */
    --warning-color: #F39C12; /* Amber */
    --error-color: #E74C3C; /* Red */
    --info-color: #3498DB; /* Blue */
}

/* Light Mode Styles */
.light-mode {
    background-color: var(--background-color-light);
    color: var(--text-color-light);
}

.light-mode .stButton > button {
    background-color: var(--primary-color);
    color: white;
}

/* Add sidebar text color for light mode */
.light-mode .stSidebar {
    color: var(--text-color-light);
}

/* Dark Mode Styles */
.dark-mode {
    background-color: var(--background-color-dark);
    color: var(--text-color-dark);
}

.dark-mode .stButton > button {
    background-color: var(--secondary-color);
    color: var(--text-color-dark);
}

/* Add sidebar text color for dark mode */
.dark-mode .stSidebar {
    color: var(--text-color-dark);
}

/* Header Animations */
.main-header {
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.sub-header {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 600;
}

/* Professional panels */
.feature-panel {
    background-color: rgba(74, 111, 227, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: 10px;
    margin: 10px 0;
    font-size: 0.95rem;
    border-radius: 0 4px 4px 0;
}

.success-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(46, 204, 113, 0.1);
    border-left: 5px solid var(--success-color);
}

.info-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(52, 152, 219, 0.1);
    border-left: 5px solid var(--info-color);
}

.warning-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(243, 156, 18, 0.1);
    border-left: 5px solid var(--warning-color);
}

.error-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(231, 76, 60, 0.1);
    border-left: 5px solid var(--error-color);
}

/* Professional card layout */
.pro-card {
    border: 1px solid rgba(74, 111, 227, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(74, 111, 227, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.pro-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 111, 227, 0.15);
}

/* Button Styling */
.stButton > button {
    width: 100%;
    border-radius: 6px;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border: none;
    transition: all 0.3s ease;
}

.stButton > button:hover {
    background-color: var(--accent-color);
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stDownloadButton > button {
    width: 100%;
    border-radius: 6px;
    background-color: var(--success-color);
    color: white;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border: none;
    transition: all 0.3s ease;
}

.stDownloadButton > button:hover {
    background-color: #27ae60;
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Sidebar Animation and Styling */
.stSidebar {
    animation: slideIn 0.5s ease-in-out;
    background-color: #222a3d; /* Dark background in both modes */
    border-right: 1px solid rgba(74, 111, 227, 0.2);
}

.dark-mode .stSidebar {
    background-color: #151a28; /* Even darker background for dark mode */
    border-right: 1px solid rgba(108, 142, 255, 0.2);
}

/* Ensure sidebar text is visible against dark background */
.stSidebar * {
    color: #ECF0F1 !important; /* Light text for dark background */
}

/* Sidebar header classes that adapt to the theme */
.sidebar-header {
    color: var(--secondary-color) !important; /* More visible on dark background */
}

.sidebar-subheader {
    color: var(--accent-color) !important; /* More visible on dark background */
}

/* Status indicators */
.status-passed {
    color: var(--success-color);
    font-weight: bold;
}

.status-failed {
    color: var(--error-color);
    font-weight: bold;
}

.status-blocked {
    color: var(--warning-color);
    font-weight: bold;
}

/* Form elements */
.stTextArea textarea {
    border-radius: 6px;
    border: 1px solid rgba(74, 111, 227, 0.3);
    transition: border 0.3s ease;
}

.stTextArea textarea:focus {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 111, 227, 0.2);
}

div[data-testid="stDataFrame"] {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

/* Admin panel styling */
.admin-small-button {
    font-size: 0.8rem !important;
    padding: 0.3rem 0.5rem !important;
    height: auto !important;
}

/* Footer style */
.caseforge-footer {
    text-align: center;
    padding: 15px;
    margin-top: 30px;
    font-size: 0.9rem;
    color: #6c757d;
    border-top: 1px solid rgba(74, 111, 227, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
