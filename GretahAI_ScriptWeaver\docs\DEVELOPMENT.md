# Development Guidelines

This document provides comprehensive guidelines for developing and maintaining GretahAI ScriptWeaver.

## Table of Contents

- [Modifying Individual Stages](#modifying-individual-stages)
- [Centralized Import System](#centralized-import-system)
- [StateManager Patterns](#statemanager-patterns)
- [UI Design Patterns](#ui-design-patterns)
- [Code Quality Standards](#code-quality-standards)

## Modifying Individual Stages

Each stage is now in its own dedicated file for easy maintenance:

- **Location**: All stage files are in the `stages/` directory
- **Naming Convention**: `stage{N}.py` where N is the stage number (1-8)
- **Function Naming**: Each stage has a main function named `stage{N}_{description}(state)`
- **State Parameter**: All stage functions accept a single `state` parameter (StateManager instance)

### Example Stage Structure

```python
"""
Stage N: Description

Module docstring explaining the stage's purpose and functionality.
"""

import logging
import streamlit as st
# Other imports...

# Configure logging
logger = logging.getLogger("ScriptWeaver.stageN")

def stageN_main_function(state):
    """
    Main stage function with comprehensive docstring.

    Args:
        state (StateManager): The application state manager instance
    """
    # Stage implementation...

    # Always use state change logging
    if state.some_value != new_value:
        state.some_value = new_value
        logger.info(f"State change: some_value = {new_value}")

    # Use st.rerun() after state changes
    st.session_state['state'] = state
    st.rerun()
```

## Centralized Import System

All stage functions are imported through `stages/__init__.py`:

- **Import Location**: Add new stage imports to `stages/__init__.py`
- **API Consistency**: All stages accessible via `from stages import stage_function`
- **Export List**: Update `__all__` list when adding new stage functions

### Adding a New Stage

```python
# In stages/__init__.py
from stages.stage9 import stage9_new_feature

# Update __all__ list
__all__ = [
    # ... existing stages ...
    'stage9_new_feature'
]
```

## StateManager Patterns

All stages follow consistent StateManager patterns:

### State Change Logging

```python
# Always log state changes with before/after values
previous_value = state.some_property
if previous_value != new_value:
    state.some_property = new_value
    logger.info(f"State change: some_property = {new_value} (was: {previous_value})")
```

### State Persistence

```python
# Always update session state after changes
st.session_state['state'] = state

# Use st.rerun() for immediate UI updates
st.rerun()
return  # Always return after st.rerun()
```

### Error Handling

```python
try:
    # Stage operations...
    pass
except Exception as e:
    error_msg = f"Error in Stage N: {str(e)}"
    logger.error(error_msg, exc_info=True)
    st.error(error_msg)
    return
```

## UI Design Patterns

All stages follow consistent UI patterns:

### Collapsible Sections

```python
with st.expander("Section Title", expanded=True):
    # Content here...
```

### Manual Progression

```python
if st.button("Proceed to Next Stage", use_container_width=True):
    # State updates and progression logic
    st.rerun()
```

### Minimalist Design

- Prioritize functionality over explanatory text
- Use visual indicators (✅, ⚠️, 🔄) for status
- Keep instructional text minimal and actionable

## Code Quality Standards

### Function Size and Responsibility

- Keep functions small and focused (max ~50 lines)
- Single responsibility principle
- Clear, descriptive function names
- Comprehensive docstrings

### Logging Standards

- Use stage-specific logger namespaces
- Log state changes only when values actually change
- Include before/after values in change logs
- Use appropriate log levels (DEBUG, INFO, WARNING, ERROR)

### Import Organization

- Group imports by type (standard library, third-party, local)
- Use absolute imports for clarity
- Avoid circular dependencies
- Keep imports minimal and specific

### Testing Guidelines

- Write unit tests for pure functions
- Test state transitions thoroughly
- Mock external dependencies (AI API calls, file operations)
- Use pytest fixtures for common test setup

### Documentation Requirements

- Update docstrings for all new functions
- Include type hints where appropriate
- Document complex algorithms or business logic
- Keep README.md updated with new features

## Migration Notes

When upgrading from the monolithic architecture:

1. **Import Changes**: Update imports to use the new modular structure
2. **State Management**: Ensure all state changes go through StateManager
3. **Logging**: Update logging calls to use stage-specific loggers
4. **Testing**: Update test imports and mocks for new structure

## Best Practices

### Performance Considerations

- Use `st.cache_data` for expensive computations
- Implement content-checking logic for file processing
- Minimize unnecessary state updates and reruns

### Security Guidelines

- Validate all user inputs
- Sanitize file uploads
- Secure API key handling
- Use environment variables for sensitive configuration

### Accessibility

- Provide clear visual feedback for user actions
- Use semantic HTML elements where possible
- Ensure keyboard navigation works properly
- Include alt text for images and icons

## Troubleshooting Development Issues

### Common Problems

1. **Circular Import Errors**: Check import hierarchy in `stages/__init__.py`
2. **State Not Persisting**: Ensure `st.session_state['state'] = state` is called
3. **UI Not Updating**: Use `st.rerun()` after state changes
4. **Logging Not Working**: Verify logger configuration and namespace

### Debug Tools

- Use the debug panel in the application for state inspection
- Check AI logs in `ai_logs/` directory for API issues
- Enable verbose logging for detailed troubleshooting
- Use browser developer tools for UI debugging

## Contributing Workflow

1. **Fork and Branch**: Create feature branches from main
2. **Follow Patterns**: Use established StateManager and UI patterns
3. **Test Thoroughly**: Test individual stages and integration
4. **Document Changes**: Update relevant documentation
5. **Code Review**: Submit PR with detailed description

For more information, see [CONTRIBUTING.md](CONTRIBUTING.md) and [API.md](API.md).
