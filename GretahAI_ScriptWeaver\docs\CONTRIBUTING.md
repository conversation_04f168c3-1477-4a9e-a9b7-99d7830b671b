# Contributing to GretahAI ScriptWeaver

Thank you for your interest in contributing to GretahAI ScriptWeaver! This document provides guidelines for contributing to the project.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Environment](#development-environment)
- [Contributing Areas](#contributing-areas)
- [Development Guidelines](#development-guidelines)
- [Submitting Changes](#submitting-changes)
- [Code Review Process](#code-review-process)

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Git for version control
- Basic understanding of Streamlit and PyTest
- Familiarity with AI/LLM integration concepts

### Fork and Clone

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/GretahAI_ScriptWeaver.git
   cd GretahAI_ScriptWeaver
   ```

3. Add the upstream repository:
   ```bash
   git remote add upstream https://github.com/original-repo/GretahAI_ScriptWeaver.git
   ```

## Development Environment

### Setup

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create configuration file:
   ```bash
   cp config.json.example config.json
   # Edit config.json with your API keys
   ```

4. Run the application:
   ```bash
   streamlit run app.py
   ```

### Testing Setup

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest tests/

# Run with coverage
pytest --cov=. tests/
```

## Contributing Areas

The modular architecture makes it easy to contribute to specific areas:

### Stage Development

**Individual Stages**: Work on specific stages in `stages/stage{N}.py`

- Each stage is independently maintainable
- Follow established StateManager patterns
- Include comprehensive logging and error handling
- Test individual stages thoroughly

**New Stages**: Add new stages following established patterns

- Create new stage file in `stages/` directory
- Update `stages/__init__.py` with imports
- Follow naming conventions and API patterns
- Include complete documentation

### Core Functionality

**AI Integration**: Enhance `core/ai.py` for better AI interactions

- Improve prompt engineering
- Add new AI model support
- Enhance error handling and retry logic
- Optimize API usage and costs

**Element Detection**: Improve `core/element_detection.py` for better element discovery

- Add new element detection strategies
- Improve element filtering and ranking
- Enhance cross-browser compatibility
- Add support for complex UI frameworks

**State Management**: Enhance `state_manager.py` for better state handling

- Add new state management features
- Improve state persistence and recovery
- Enhance debugging and inspection tools
- Optimize performance for large datasets

### UI/UX Improvements

**UI Components**: Add reusable components to `ui/components.py`

- Create new Streamlit components
- Improve existing component functionality
- Add accessibility features
- Enhance visual design and user experience

**Styling**: Improve visual design and user experience

- Update CSS and theming
- Improve responsive design
- Add dark/light mode support
- Enhance mobile compatibility

## Development Guidelines

### Code Standards

1. **Follow PEP 8**: Use consistent Python coding style
2. **Type Hints**: Include type hints for all function parameters and returns
3. **Docstrings**: Write comprehensive docstrings for all functions and classes
4. **Error Handling**: Include proper exception handling with specific error types
5. **Logging**: Use appropriate logging levels and include context information

### StateManager Patterns

All contributions must follow established StateManager patterns:

```python
# State change logging
if state.property != new_value:
    old_value = state.property
    state.property = new_value
    logger.info(f"State change: property = {new_value} (was: {old_value})")

# State persistence
st.session_state['state'] = state
st.rerun()
return  # Always return after st.rerun()
```

### UI Design Principles

- **Minimalist Design**: Prioritize functionality over explanatory text
- **Collapsible Sections**: Use `st.expander()` for organized content
- **Manual Progression**: Require explicit user confirmation for stage transitions
- **Visual Feedback**: Use clear indicators (✅, ⚠️, 🔄) for status

### Testing Requirements

1. **Unit Tests**: Write tests for all new functions
2. **Integration Tests**: Test stage interactions and workflows
3. **Mock External Dependencies**: Mock AI API calls and file operations
4. **Test Coverage**: Maintain high test coverage (>80%)

### Documentation Requirements

1. **Code Documentation**: Update docstrings and inline comments
2. **API Documentation**: Update `docs/API.md` for new functions
3. **Development Guidelines**: Update `docs/DEVELOPMENT.md` for new patterns
4. **README Updates**: Update main README.md for new features

## Submitting Changes

### Branch Naming

Use descriptive branch names:
- `feature/stage-optimization` - New features
- `bugfix/state-persistence` - Bug fixes
- `docs/api-updates` - Documentation updates
- `refactor/element-detection` - Code refactoring

### Commit Messages

Follow conventional commit format:
```
type(scope): description

- feat(stage6): add script optimization features
- fix(state): resolve state persistence issue
- docs(api): update function signatures
- refactor(core): improve element detection logic
```

### Pull Request Process

1. **Create Feature Branch**:
   ```bash
   git checkout -b feature/amazing-feature
   ```

2. **Make Changes**: Follow development guidelines and test thoroughly

3. **Commit Changes**:
   ```bash
   git add .
   git commit -m "feat(scope): add amazing feature"
   ```

4. **Push to Fork**:
   ```bash
   git push origin feature/amazing-feature
   ```

5. **Create Pull Request**: Open PR with detailed description

### Pull Request Template

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## Code Review Process

### Review Criteria

1. **Architectural Consistency**: Changes follow established patterns
2. **Code Quality**: Clean, readable, and maintainable code
3. **Testing**: Adequate test coverage and passing tests
4. **Documentation**: Complete and accurate documentation
5. **Performance**: No significant performance regressions

### Review Timeline

- **Initial Review**: Within 2-3 business days
- **Follow-up Reviews**: Within 1-2 business days
- **Final Approval**: After all feedback addressed

### Reviewer Responsibilities

- Provide constructive feedback
- Test changes locally when possible
- Verify documentation accuracy
- Check for breaking changes
- Ensure architectural consistency

## Getting Help

### Communication Channels

- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Email**: <EMAIL> for direct support

### Resources

- [Development Guidelines](DEVELOPMENT.md)
- [API Documentation](API.md)
- [Project README](../README.md)
- [Streamlit Documentation](https://docs.streamlit.io/)
- [PyTest Documentation](https://docs.pytest.org/)

## Recognition

Contributors will be recognized in:
- Project README.md contributors section
- Release notes for significant contributions
- GitHub contributor graphs and statistics

Thank you for contributing to GretahAI ScriptWeaver!
